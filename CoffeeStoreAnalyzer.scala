import org.apache.spark.{SparkConf, SparkContext}

/**
 * 咖啡连锁店数据分析器
 * 对咖啡销售数据进行全面分析
 */
object CoffeeStoreAnalyzer {
  
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("CoffeeStoreAnalyzer")
      .setMaster("local[*]")
      .set("spark.sql.adaptive.enabled", "true")
      .set("spark.sql.adaptive.coalescePartitions.enabled", "true")
    
    // 创建SparkContext
    val sc = new SparkContext(conf)
    
    try {
      // 输入和输出路径
      val inputPath = "/home/<USER>/spark/CoffeeChain.csv"
      val outputBasePath = "/home/<USER>/spark/coffee_analysis"
      
      println("开始分析咖啡连锁店数据...")
      println(s"输入文件: $inputPath")
      println(s"输出基础目录: $outputBasePath")
      
      // 执行分析
      analyzeCoffeeData(sc, inputPath, outputBasePath)
      
      println("咖啡连锁店数据分析完成！")
      
    } catch {
      case e: Exception =>
        println(s"分析过程中出现错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  /**
   * 分析咖啡数据
   */
  def analyzeCoffeeData(sc: SparkContext, inputPath: String, outputBasePath: String): Unit = {
    // 读取CSV文件
    val rawData = sc.textFile(inputPath)
    
    // 获取表头
    val header = rawData.first()
    println(s"数据表头: $header")
    
    // 过滤掉表头，解析数据
    val dataRDD = rawData.filter(_ != header).map(parseCSVLine)
    
    // 缓存数据以提高性能
    dataRDD.cache()
    
    val totalRecords = dataRDD.count()
    println(s"总记录数: $totalRecords")
    
    // 1. 数据预处理和基本统计
    performDataPreprocessing(sc, dataRDD, s"$outputBasePath/preprocessing")
    
    // 2. 咖啡销售量排名
    analyzeSalesRanking(sc, dataRDD, s"$outputBasePath/sales_ranking")
    
    // 3. 销售量分布分析
    analyzeSalesDistribution(sc, dataRDD, s"$outputBasePath/distribution_analysis")
    
    // 4. 州与销售量关系分析
    analyzeStateVsSales(sc, dataRDD, s"$outputBasePath/state_sales_analysis")
    
    // 5. 市场与销售量关系分析
    analyzeMarketVsSales(sc, dataRDD, s"$outputBasePath/market_sales_analysis")
    
    // 6. 利润和售价分析
    analyzeProfitAndPrice(sc, dataRDD, s"$outputBasePath/profit_price_analysis")
    
    // 清理缓存
    dataRDD.unpersist()
  }
  
  /**
   * 解析CSV行数据
   */
  def parseCSVLine(line: String): CoffeeRecord = {
    val parts = line.split(",").map(_.trim.replaceAll("\"", ""))
    
    CoffeeRecord(
      area_code = if (parts.length > 0) parts(0) else "",
      area = if (parts.length > 1) parts(1) else "",
      sales = if (parts.length > 2 && parts(2).nonEmpty) parts(2).toDouble else 0.0,
      cogs = if (parts.length > 3 && parts(3).nonEmpty) parts(3).toDouble else 0.0,
      total_expenses = if (parts.length > 4 && parts(4).nonEmpty) parts(4).toDouble else 0.0,
      marketing = if (parts.length > 5 && parts(5).nonEmpty) parts(5).toDouble else 0.0,
      inventory = if (parts.length > 6 && parts(6).nonEmpty) parts(6).toDouble else 0.0,
      budget_profit = if (parts.length > 7 && parts(7).nonEmpty) parts(7).toDouble else 0.0,
      budget_cogs = if (parts.length > 8 && parts(8).nonEmpty) parts(8).toDouble else 0.0,
      budget_margin = if (parts.length > 9 && parts(9).nonEmpty) parts(9).toDouble else 0.0,
      date = if (parts.length > 10) parts(10) else "",
      product_line = if (parts.length > 11) parts(11) else "",
      product_type = if (parts.length > 12) parts(12) else "",
      product = if (parts.length > 13) parts(13) else "",
      type_name = if (parts.length > 14) parts(14) else "",
      market_size = if (parts.length > 15) parts(15) else "",
      market = if (parts.length > 16) parts(16) else "",
      state = if (parts.length > 17) parts(17) else ""
    )
  }
  
  /**
   * 数据预处理
   */
  def performDataPreprocessing(sc: SparkContext, dataRDD: org.apache.spark.rdd.RDD[CoffeeRecord], outputPath: String): Unit = {
    val results = Array(
      "=== 咖啡连锁店数据预处理报告 ===",
      s"处理时间: ${new java.util.Date()}",
      "",
      "=== 数据质量检查 ===",
      s"总记录数: ${dataRDD.count()}",
      s"有效销售记录数: ${dataRDD.filter(_.sales > 0).count()}",
      s"空值记录数: ${dataRDD.filter(r => r.product.isEmpty || r.state.isEmpty).count()}",
      "",
      "=== 数据范围统计 ===",
      s"销售额范围: ${dataRDD.map(_.sales).min()} - ${dataRDD.map(_.sales).max()}",
      s"成本范围: ${dataRDD.map(_.cogs).min()} - ${dataRDD.map(_.cogs).max()}",
      s"利润范围: ${dataRDD.map(_.budget_profit).min()} - ${dataRDD.map(_.budget_profit).max()}"
    )
    
    sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
    println("数据预处理完成")
  }
  
  /**
   * 销售量排名分析
   */
  def analyzeSalesRanking(sc: SparkContext, dataRDD: org.apache.spark.rdd.RDD[CoffeeRecord], outputPath: String): Unit = {
    val productSales = dataRDD
      .map(record => (record.product, record.sales))
      .reduceByKey(_ + _)
      .sortBy(_._2, false)
      .take(20)

    val results = Array("=== 咖啡产品销售量排名 (前20名) ===", "排名\t产品名称\t总销售额") ++
      productSales.zipWithIndex.map { case ((product, sales), index) =>
        s"${index + 1}\t$product\t${f"$sales%.2f"}"
      }

    sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
    println("销售量排名分析完成")
  }

  /**
   * 销售量分布分析
   */
  def analyzeSalesDistribution(sc: SparkContext, dataRDD: org.apache.spark.rdd.RDD[CoffeeRecord], outputPath: String): Unit = {
    val salesStats = dataRDD.map(_.sales)
    val avgSales = salesStats.mean()
    val maxSales = salesStats.max()
    val minSales = salesStats.min()
    val totalSales = salesStats.sum()

    val results = Array(
      "=== 咖啡销售量分布分析 ===",
      s"总销售额: ${f"$totalSales%.2f"}",
      s"平均销售额: ${f"$avgSales%.2f"}",
      s"最高销售额: ${f"$maxSales%.2f"}",
      s"最低销售额: ${f"$minSales%.2f"}"
    )

    sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
    println("销售量分布分析完成")
  }

  /**
   * 州与销售量关系分析
   */
  def analyzeStateVsSales(sc: SparkContext, dataRDD: org.apache.spark.rdd.RDD[CoffeeRecord], outputPath: String): Unit = {
    val stateSales = dataRDD
      .map(record => (record.state, record.sales))
      .reduceByKey(_ + _)
      .sortBy(_._2, false)

    val results = Array("=== 各州销售量分析 ===", "州名\t总销售额") ++
      stateSales.collect().map { case (state, sales) =>
        s"$state\t${f"$sales%.2f"}"
      }

    sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
    println("州与销售量关系分析完成")
  }

  /**
   * 市场与销售量关系分析
   */
  def analyzeMarketVsSales(sc: SparkContext, dataRDD: org.apache.spark.rdd.RDD[CoffeeRecord], outputPath: String): Unit = {
    val marketSales = dataRDD
      .map(record => (record.market, record.sales))
      .reduceByKey(_ + _)
      .sortBy(_._2, false)

    val results = Array("=== 各市场销售量分析 ===", "市场\t总销售额") ++
      marketSales.collect().map { case (market, sales) =>
        s"$market\t${f"$sales%.2f"}"
      }

    sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
    println("市场与销售量关系分析完成")
  }

  /**
   * 利润和售价分析
   */
  def analyzeProfitAndPrice(sc: SparkContext, dataRDD: org.apache.spark.rdd.RDD[CoffeeRecord], outputPath: String): Unit = {
    val profitStats = dataRDD.map(_.budget_profit)
    val avgProfit = profitStats.mean()
    val totalProfit = profitStats.sum()

    val cogsStats = dataRDD.map(_.cogs)
    val avgCogs = cogsStats.mean()
    val totalCogs = cogsStats.sum()

    val results = Array(
      "=== 利润和成本分析 ===",
      s"总利润: ${f"$totalProfit%.2f"}",
      s"平均利润: ${f"$avgProfit%.2f"}",
      s"总成本: ${f"$totalCogs%.2f"}",
      s"平均成本: ${f"$avgCogs%.2f"}",
      s"利润率: ${f"${(totalProfit / (totalProfit + totalCogs)) * 100}%.2f"}%"
    )

    sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
    println("利润和售价分析完成")
  }
}

/**
 * 咖啡记录数据结构
 */
case class CoffeeRecord(
  area_code: String,
  area: String,
  sales: Double,
  cogs: Double,
  total_expenses: Double,
  marketing: Double,
  inventory: Double,
  budget_profit: Double,
  budget_cogs: Double,
  budget_margin: Double,
  date: String,
  product_line: String,
  product_type: String,
  product: String,
  type_name: String,
  market_size: String,
  market: String,
  state: String
)
