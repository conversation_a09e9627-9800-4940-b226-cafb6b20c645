# 大数据实时处理技术期末考试项目

## 项目简介

本项目是山东协和学院《大数据实时处理技术》期末考试的完整实现，包含人口年龄统计和咖啡连锁店数据分析两个主要任务。

**虚拟机路径**: `/home/<USER>/spark/`

## 项目结构

```
/home/<USER>/spark/
├── PopulationDataGenerator.scala    # 人口数据生成器
├── PopulationAgeAnalyzer.scala      # 人口年龄分析器  
├── CoffeeStoreAnalyzer.scala        # 咖啡店数据分析器
├── spark_analysis_commands.scala    # Spark Shell命令脚本
├── execute_analysis.sh              # 自动化执行脚本
├── 项目运行指南.md                   # 详细运行指南
├── 期末考试实训报告.md               # 实训报告模板
├── README.md                        # 项目说明文件
├── 任务要求.md                      # 考试任务要求
└── CoffeeChain.csv                  # 咖啡连锁店数据文件
```

## 快速开始

### 一键执行（推荐）
```bash
cd /home/<USER>/spark/
chmod +x execute_analysis.sh
./execute_analysis.sh
```

### 使用Spark Shell
```bash
cd /home/<USER>/spark/
spark-shell --driver-memory 2g --executor-memory 2g
# 然后复制粘贴 spark_analysis_commands.scala 中的代码
```

## 主要功能

### 任务一：人口年龄统计
- 生成10万条人口数据（序号+年龄）
- 使用Spark RDD计算平均年龄
- 统计年龄分布情况
- 生成详细分析报告

### 任务二：咖啡连锁店数据分析
- 数据预处理和质量检查
- 咖啡产品销售排名分析
- 各州销售量关系分析
- 市场与销售量关系分析
- 利润和成本分析

## 技术特色

1. **完整的数据处理流程**: 从数据生成到分析结果输出
2. **丰富的分析维度**: 多角度的数据分析和统计
3. **优化的性能配置**: 合理的内存配置和缓存策略
4. **完善的错误处理**: 异常处理和数据验证机制
5. **自动化执行**: 一键运行所有分析任务
6. **详细的文档**: 完整的运行指南和报告模板

## 运行结果

### 人口年龄分析结果
- 总人口数量统计
- 平均年龄计算
- 年龄分布分析
- 最大/最小年龄统计

### 咖啡数据分析结果
- 产品销售排名
- 地区销售分析
- 市场表现分析
- 财务指标分析

## 环境要求

- Java 8 或 Java 11
- Scala 2.12.x
- Apache Spark 3.x
- Linux 操作系统
- 至少2GB可用内存

## 文件说明

| 文件名 | 功能描述 |
|--------|----------|
| PopulationDataGenerator.scala | 生成人口年龄数据文件 |
| PopulationAgeAnalyzer.scala | 分析人口年龄数据 |
| CoffeeStoreAnalyzer.scala | 分析咖啡连锁店数据 |
| spark_analysis_commands.scala | Spark Shell交互式命令 |
| execute_analysis.sh | 自动化执行脚本 |
| 项目运行指南.md | 详细的运行说明 |
| 期末考试实训报告.md | 实训报告模板 |

## 输出文件

运行完成后会生成以下结果文件：

```
/home/<USER>/spark/
├── population_data.txt                    # 生成的人口数据
├── population_analysis_result/            # 人口分析结果
│   └── part-00000
└── coffee_analysis/                       # 咖啡分析结果
    ├── preprocessing/
    ├── sales_ranking/
    ├── distribution_analysis/
    ├── state_sales_analysis/
    ├── market_sales_analysis/
    └── profit_price_analysis/
```

## 使用说明

1. **环境准备**: 确保Java、Scala、Spark环境正确配置
2. **文件准备**: 确保CoffeeChain.csv数据文件存在
3. **执行分析**: 运行自动化脚本或手动执行
4. **查看结果**: 检查生成的分析结果文件
5. **完成报告**: 使用模板完成实训报告

## 注意事项

1. 确保虚拟机有足够的内存空间
2. 检查文件路径的正确性
3. 确保用户有读写权限
4. 根据实际情况调整内存配置

## 技术支持

如遇到问题，请：
1. 检查环境配置
2. 查看错误日志
3. 参考运行指南
4. 检查文件权限

## 版本信息

- **项目版本**: 2.0
- **创建时间**: 2024年
- **适用课程**: 大数据实时处理技术
- **目标用户**: 2022级本科生

## 许可证

本项目仅用于教学目的，请勿用于商业用途。

---

**祝您实训顺利！**
