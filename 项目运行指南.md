# 大数据实时处理技术期末考试运行指南

## 项目概述

本项目是山东协和学院《大数据实时处理技术》期末考试项目，包含两个主要任务：
1. 使用RDD编程统计人口平均年龄
2. 咖啡连锁店数据处理分析

**虚拟机路径**: `/home/<USER>/spark/`

## 环境要求

### 必需软件
- Java 8 或 Java 11
- Scala 2.12.x
- Apache Spark 3.x
- Linux 操作系统

### 环境检查命令
```bash
# 检查Java版本
java -version

# 检查Scala版本
scala -version

# 检查Spark版本
spark-submit --version
```

## 项目文件结构

```
/home/<USER>/spark/
├── PopulationDataGenerator.scala    # 人口数据生成器
├── PopulationAgeAnalyzer.scala      # 人口年龄分析器
├── CoffeeStoreAnalyzer.scala        # 咖啡店数据分析器
├── spark_analysis_commands.scala    # Spark Shell命令脚本
├── execute_analysis.sh              # 自动化执行脚本
├── CoffeeChain.csv                  # 咖啡连锁店数据文件
└── 项目运行指南.md                   # 本文件
```

## 运行方式

### 方式一：使用自动化脚本（推荐）

1. **给脚本执行权限**
```bash
cd /home/<USER>/spark/
chmod +x execute_analysis.sh
```

2. **运行脚本**
```bash
./execute_analysis.sh
```

脚本会自动完成：
- 环境检查
- 人口数据生成
- Scala程序编译
- Spark程序运行
- 结果展示

### 方式二：使用Spark Shell（交互式）

1. **启动Spark Shell**
```bash
cd /home/<USER>/spark/
spark-shell --driver-memory 2g --executor-memory 2g
```

2. **在Spark Shell中执行**
```scala
// 复制粘贴 spark_analysis_commands.scala 文件中的代码
// 可以分段执行，观察每步的结果
```

3. **退出Spark Shell**
```scala
:quit
```

### 方式三：分步手动执行

#### 步骤1：生成人口数据
```bash
cd /home/<USER>/spark/
scala PopulationDataGenerator.scala
```

#### 步骤2：编译Scala程序
```bash
# 编译人口年龄分析程序
scalac -cp "$SPARK_HOME/jars/*" PopulationAgeAnalyzer.scala

# 编译咖啡数据分析程序
scalac -cp "$SPARK_HOME/jars/*" CoffeeStoreAnalyzer.scala
```

#### 步骤3：运行Spark程序
```bash
# 运行人口年龄分析
spark-submit --class PopulationAgeAnalyzer --master local[*] --driver-memory 2g .

# 运行咖啡数据分析
spark-submit --class CoffeeStoreAnalyzer --master local[*] --driver-memory 2g .
```

## 查看结果

### 生成的文件
```bash
# 查看所有生成的文件
ls -la /home/<USER>/spark/

# 查看人口数据文件（前10行）
head -10 /home/<USER>/spark/population_data.txt
```

### 分析结果
```bash
# 人口年龄分析结果
cat /home/<USER>/spark/population_analysis_result/part-00000

# 咖啡数据预处理结果
cat /home/<USER>/spark/coffee_analysis/preprocessing/part-00000

# 咖啡销售排名结果
cat /home/<USER>/spark/coffee_analysis/sales_ranking/part-00000

# 咖啡销售分布分析
cat /home/<USER>/spark/coffee_analysis/distribution_analysis/part-00000

# 各州销售分析
cat /home/<USER>/spark/coffee_analysis/state_sales_analysis/part-00000

# 市场销售分析
cat /home/<USER>/spark/coffee_analysis/market_sales_analysis/part-00000

# 利润分析
cat /home/<USER>/spark/coffee_analysis/profit_price_analysis/part-00000
```

## 常见问题解决

### 1. 内存不足错误
```bash
# 增加Spark内存配置
spark-shell --driver-memory 4g --executor-memory 4g
# 或者在运行时指定
spark-submit --driver-memory 4g --executor-memory 4g --class ClassName .
```

### 2. 找不到文件错误
```bash
# 确保在正确目录
cd /home/<USER>/spark/
pwd

# 检查文件是否存在
ls -la CoffeeChain.csv
ls -la *.scala
```

### 3. 编译错误
```bash
# 检查SPARK_HOME环境变量
echo $SPARK_HOME

# 如果未设置，手动设置
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH
```

### 4. 权限问题
```bash
# 给目录和文件适当权限
chmod 755 /home/<USER>/spark/
chmod 644 /home/<USER>/spark/*.csv
chmod 644 /home/<USER>/spark/*.scala
chmod +x /home/<USER>/spark/*.sh
```

### 5. Java版本问题
```bash
# 检查Java版本
java -version

# 如果版本不对，切换Java版本
sudo alternatives --config java
```

## 实训报告截图建议

为完成实训报告，建议截取以下关键画面：

### 1. 环境准备截图
- `java -version` 输出
- `scala -version` 输出
- `spark-submit --version` 输出
- 项目文件列表 `ls -la`

### 2. 数据生成截图
- 运行 `scala PopulationDataGenerator.scala` 的过程
- `head -10 population_data.txt` 显示生成的数据

### 3. 程序编译截图
- Scala程序编译过程
- 编译成功的提示信息

### 4. Spark运行截图
- Spark程序启动画面
- 人口年龄分析的运行过程和结果
- 咖啡数据分析的运行过程和结果

### 5. 结果展示截图
- 各个分析结果文件的内容
- 统计数据和分析报告

## 项目特色

本项目相比传统实现有以下改进：

1. **更新的虚拟机路径**: 使用 `/home/<USER>/spark/` 替代 `/home/<USER>/me/`
2. **更丰富的数据分析**: 包含多维度的咖啡数据分析
3. **更完善的错误处理**: 增加了异常处理和数据验证
4. **更详细的结果输出**: 提供更全面的统计信息和分析报告
5. **自动化执行**: 提供一键执行脚本，简化操作流程
6. **更好的代码结构**: 采用面向对象设计，代码更清晰

## 注意事项

1. **路径一致性**: 确保所有文件都在 `/home/<USER>/spark/` 目录下
2. **数据文件完整性**: 确保 `CoffeeChain.csv` 文件完整且格式正确
3. **内存配置**: 根据虚拟机配置适当调整Spark内存参数
4. **网络连接**: 如需下载依赖包，确保网络连接正常
5. **用户权限**: 确保当前用户有读写目录的权限

## 技术支持

如遇到问题，请检查：
1. 环境配置是否正确
2. 文件路径是否正确
3. 权限设置是否合适
4. 内存配置是否充足

祝您实训顺利完成！
