#!/bin/bash

# ===================================================================
# 大数据实时处理技术期末考试 - 自动化执行脚本
# 虚拟机路径: /home/<USER>/spark/
# ===================================================================

echo "=========================================="
echo "大数据实时处理技术期末考试执行脚本"
echo "虚拟机路径: /home/<USER>/spark/"
echo "=========================================="

# 设置工作目录
WORK_DIR="/home/<USER>/spark"
cd $WORK_DIR

echo "当前工作目录: $(pwd)"

# 检查环境
echo ""
echo "=== 环境检查 ==="
echo "检查Java版本:"
java -version

echo ""
echo "检查Scala版本:"
scala -version

echo ""
echo "检查Spark版本:"
spark-submit --version

echo ""
echo "检查必要文件:"
if [ -f "CoffeeChain.csv" ]; then
    echo "✓ CoffeeChain.csv 文件存在"
    echo "文件大小: $(du -h CoffeeChain.csv | cut -f1)"
else
    echo "✗ CoffeeChain.csv 文件不存在，请确保数据文件在正确位置"
    exit 1
fi

if [ -f "PopulationDataGenerator.scala" ]; then
    echo "✓ PopulationDataGenerator.scala 文件存在"
else
    echo "✗ PopulationDataGenerator.scala 文件不存在"
    exit 1
fi

if [ -f "PopulationAgeAnalyzer.scala" ]; then
    echo "✓ PopulationAgeAnalyzer.scala 文件存在"
else
    echo "✗ PopulationAgeAnalyzer.scala 文件不存在"
    exit 1
fi

if [ -f "CoffeeStoreAnalyzer.scala" ]; then
    echo "✓ CoffeeStoreAnalyzer.scala 文件存在"
else
    echo "✗ CoffeeStoreAnalyzer.scala 文件不存在"
    exit 1
fi

echo ""
echo "=== 开始执行分析任务 ==="

# 方法1: 生成人口数据
echo ""
echo "1. 生成人口年龄数据..."
scala PopulationDataGenerator.scala

if [ $? -eq 0 ]; then
    echo "✓ 人口数据生成成功"
    echo "生成的数据文件前10行:"
    head -10 population_data.txt
else
    echo "✗ 人口数据生成失败"
    exit 1
fi

# 方法2: 编译和运行Spark程序
echo ""
echo "2. 编译Scala程序..."

# 编译人口年龄分析程序
echo "编译 PopulationAgeAnalyzer.scala..."
scalac -cp "$SPARK_HOME/jars/*" PopulationAgeAnalyzer.scala

if [ $? -eq 0 ]; then
    echo "✓ PopulationAgeAnalyzer 编译成功"
else
    echo "✗ PopulationAgeAnalyzer 编译失败"
    exit 1
fi

# 编译咖啡数据分析程序
echo "编译 CoffeeStoreAnalyzer.scala..."
scalac -cp "$SPARK_HOME/jars/*" CoffeeStoreAnalyzer.scala

if [ $? -eq 0 ]; then
    echo "✓ CoffeeStoreAnalyzer 编译成功"
else
    echo "✗ CoffeeStoreAnalyzer 编译失败"
    exit 1
fi

echo ""
echo "3. 运行人口年龄分析..."
spark-submit --class PopulationAgeAnalyzer --master local[*] --driver-memory 2g --executor-memory 2g .

if [ $? -eq 0 ]; then
    echo "✓ 人口年龄分析完成"
else
    echo "✗ 人口年龄分析失败"
fi

echo ""
echo "4. 运行咖啡数据分析..."
spark-submit --class CoffeeStoreAnalyzer --master local[*] --driver-memory 2g --executor-memory 2g .

if [ $? -eq 0 ]; then
    echo "✓ 咖啡数据分析完成"
else
    echo "✗ 咖啡数据分析失败"
fi

echo ""
echo "=== 查看分析结果 ==="

echo ""
echo "生成的文件和目录:"
ls -la

echo ""
echo "人口年龄分析结果:"
if [ -d "population_analysis_result" ]; then
    echo "--- 人口分析结果内容 ---"
    cat population_analysis_result/part-00000
else
    echo "人口分析结果目录不存在"
fi

echo ""
echo "咖啡数据分析结果:"
if [ -d "coffee_analysis" ]; then
    echo "--- 数据预处理结果 ---"
    if [ -d "coffee_analysis/preprocessing" ]; then
        cat coffee_analysis/preprocessing/part-00000
    fi
    
    echo ""
    echo "--- 销售排名结果 ---"
    if [ -d "coffee_analysis/sales_ranking" ]; then
        cat coffee_analysis/sales_ranking/part-00000
    fi
    
    echo ""
    echo "--- 销售分布分析结果 ---"
    if [ -d "coffee_analysis/distribution_analysis" ]; then
        cat coffee_analysis/distribution_analysis/part-00000
    fi
else
    echo "咖啡分析结果目录不存在"
fi

echo ""
echo "=== 执行完成 ==="
echo "所有分析任务已完成！"
echo ""
echo "结果文件位置:"
echo "- 人口数据文件: $WORK_DIR/population_data.txt"
echo "- 人口分析结果: $WORK_DIR/population_analysis_result/"
echo "- 咖啡分析结果: $WORK_DIR/coffee_analysis/"
echo ""
echo "可以使用以下命令查看详细结果:"
echo "cat $WORK_DIR/population_analysis_result/part-00000"
echo "cat $WORK_DIR/coffee_analysis/sales_ranking/part-00000"
echo ""
echo "实训报告截图建议:"
echo "1. 环境检查截图 (java -version, scala -version, spark-submit --version)"
echo "2. 数据生成过程截图"
echo "3. Spark程序运行过程截图"
echo "4. 分析结果截图"
echo "5. 文件列表截图 (ls -la)"
